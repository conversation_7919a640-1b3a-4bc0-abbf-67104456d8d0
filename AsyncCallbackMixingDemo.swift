import Foundation

// DEMONSTRATION: Why mixing async and callbacks is problematic

print("=== DEMONSTRATING THE TECHNICAL PROBLEMS ===\n")

// Problem 1: Unpredictable timing
func mixedFunction(_ completion: @escaping (String) -> Void) async {
    print("  mixedFunction: Starting")
    
    // Simulate some async work
    try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
    
    print("  mixedFunction: About to call completion")
    completion("Hello from completion")
    
    print("  mixedFunction: About to return")
    // Function returns here, but completion might still be running
}

// Problem 2: What does await actually wait for?
func demonstrateProblem1() async {
    print("1. Before await")
    
    await mixedFunction { result in
        print("  Completion handler called with: \(result)")
        // When does this run relative to "3. After await"?
    }
    
    print("2. After await - but when did the completion run?")
    print()
}

// Problem 3: Even worse - delayed completion
func reallyBadMixedFunction(_ completion: @escaping (String) -> Void) async {
    print("  reallyBadMixedFunction: Starting")
    
    // Call completion AFTER the function returns (on a different queue)
    DispatchQueue.global().asyncAfter(deadline: .now() + 2) {
        print("  Delayed completion running 2 seconds later!")
        completion("This runs after await finished!")
    }
    
    print("  reallyBadMixedFunction: Returning immediately")
    // Function returns here, but completion won't run for 2 more seconds!
}

func demonstrateProblem2() async {
    print("1. Before await")
    
    await reallyBadMixedFunction { result in
        print("  Delayed completion: \(result)")
    }
    
    print("2. After await - but completion hasn't run yet!")
    print("   Waiting 3 seconds to see the delayed completion...")
    
    try? await Task.sleep(nanoseconds: 3_000_000_000) // 3 seconds
    print("3. Now the delayed completion has run")
    print()
}

// Problem 4: Error handling becomes a mess
func mixedFunctionWithError(_ completion: @escaping (Result<String, Error>) -> Void) async throws {
    // Which error handling mechanism do we use?
    // - throw for async/await?
    // - .failure in the Result for completion?
    // - Both? Neither?
    
    if Bool.random() {
        throw NSError(domain: "AsyncError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Async error"])
    } else {
        completion(.failure(NSError(domain: "CallbackError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Callback error"])))
    }
}

func demonstrateProblem3() async {
    print("Error handling becomes confusing:")
    
    do {
        try await mixedFunctionWithError { result in
            switch result {
            case .success(let value):
                print("  Completion success: \(value)")
            case .failure(let error):
                print("  Completion error: \(error.localizedDescription)")
            }
        }
        print("  No async error thrown")
    } catch {
        print("  Async error caught: \(error.localizedDescription)")
    }
    print()
}

// SOLUTION: Proper separation of concerns
func properAsyncFunction() async -> String {
    print("  properAsyncFunction: Starting")
    try? await Task.sleep(nanoseconds: 1_000_000_000)
    print("  properAsyncFunction: Returning")
    return "Hello from async"
}

func properCallbackFunction(_ completion: @escaping (String) -> Void) {
    print("  properCallbackFunction: Starting")
    DispatchQueue.global().asyncAfter(deadline: .now() + 1) {
        print("  properCallbackFunction: Calling completion")
        completion("Hello from callback")
    }
    print("  properCallbackFunction: Returning immediately")
}

func demonstrateSolution() async {
    print("SOLUTION - Use each pattern properly:")
    
    // Async/await: predictable, structured
    print("1. Using pure async:")
    let asyncResult = await properAsyncFunction()
    print("   Got result: \(asyncResult)")
    
    // Callback: explicit about asynchronous nature
    print("2. Using pure callback:")
    await withCheckedContinuation { continuation in
        properCallbackFunction { result in
            print("   Got result: \(result)")
            continuation.resume()
        }
    }
    print()
}

// Run all demonstrations
Task {
    await demonstrateProblem1()
    await demonstrateProblem2()
    await demonstrateProblem3()
    await demonstrateSolution()
    
    print("=== SUMMARY ===")
    print("• Async functions return when they're done")
    print("• Completion handlers can be called anytime")
    print("• Mixing them creates unpredictable timing")
    print("• Error handling becomes ambiguous")
    print("• Use one pattern or the other, not both!")
    
    exit(0)
}

RunLoop.main.run()
