import Foundation

print("=== CALLING ASYNC CLOSURE PARAMETERS ===\n")

// EXAMPLE 1: Function that takes an async closure
func processWithAsyncClosure(_ asyncWork: () async -> String) async {
    print("1. About to call async closure")
    
    // You MUST use await to call an async closure
    let result = await asyncWork()
    
    print("2. Got result from async closure: \(result)")
}

// EXAMPLE 2: Async closure that throws
func processWithThrowingAsyncClosure(_ asyncWork: () async throws -> String) async throws {
    print("1. About to call throwing async closure")
    
    // Use await + try for throwing async closures
    let result = try await asyncWork()
    
    print("2. Got result: \(result)")
}

// EXAMPLE 3: Async closure with parameters
func processWithParameterizedAsyncClosure(_ asyncWork: (String) async -> String) async {
    print("1. About to call async closure with parameter")
    
    let result = await asyncWork("Hello from parameter")
    
    print("2. Got result: \(result)")
}

// EXAMPLE 4: Multiple async closures
func processMultipleAsyncClosures(
    _ work1: () async -> String,
    _ work2: () async -> String
) async {
    print("1. Calling first async closure")
    let result1 = await work1()
    
    print("2. Calling second async closure")
    let result2 = await work2()
    
    print("3. Results: \(result1), \(result2)")
}

// EXAMPLE 5: Async closure in a loop
func processAsyncClosuresInLoop(_ closures: [() async -> String]) async {
    print("Processing \(closures.count) async closures:")
    
    for (index, closure) in closures.enumerated() {
        print("  Calling closure \(index + 1)")
        let result = await closure()
        print("  Result \(index + 1): \(result)")
    }
}

// DEMONSTRATION
func runExamples() async {
    print("EXAMPLE 1: Basic async closure")
    await processWithAsyncClosure {
        // This closure is async, so it can use await
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        return "Async work completed!"
    }
    print()
    
    print("EXAMPLE 2: Throwing async closure")
    do {
        try await processWithThrowingAsyncClosure {
            try await Task.sleep(nanoseconds: 500_000_000)
            if Bool.random() {
                throw NSError(domain: "TestError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Random error"])
            }
            return "Throwing async work completed!"
        }
    } catch {
        print("Caught error: \(error.localizedDescription)")
    }
    print()
    
    print("EXAMPLE 3: Async closure with parameters")
    await processWithParameterizedAsyncClosure { parameter in
        try? await Task.sleep(nanoseconds: 500_000_000)
        return "Processed: \(parameter)"
    }
    print()
    
    print("EXAMPLE 4: Multiple async closures")
    await processMultipleAsyncClosures(
        {
            try? await Task.sleep(nanoseconds: 300_000_000)
            return "First"
        },
        {
            try? await Task.sleep(nanoseconds: 200_000_000)
            return "Second"
        }
    )
    print()
    
    print("EXAMPLE 5: Async closures in loop")
    let asyncClosures: [() async -> String] = [
        {
            try? await Task.sleep(nanoseconds: 100_000_000)
            return "Task A"
        },
        {
            try? await Task.sleep(nanoseconds: 200_000_000)
            return "Task B"
        },
        {
            try? await Task.sleep(nanoseconds: 150_000_000)
            return "Task C"
        }
    ]
    
    await processAsyncClosuresInLoop(asyncClosures)
    print()
}

// COMMON PATTERNS AND SYNTAX

print("COMMON ASYNC CLOSURE PATTERNS:")
print()

// Pattern 1: Simple async closure parameter
func pattern1(_ work: () async -> Void) async {
    await work()
}

// Pattern 2: Async closure with return value
func pattern2(_ work: () async -> String) async -> String {
    return await work()
}

// Pattern 3: Throwing async closure
func pattern3(_ work: () async throws -> String) async throws -> String {
    return try await work()
}

// Pattern 4: Async closure with parameters
func pattern4(_ work: (String, Int) async -> String) async -> String {
    return await work("test", 42)
}

// Pattern 5: Escaping async closure (stored for later)
func pattern5(_ work: @escaping () async -> String) {
    Task {
        let result = await work()
        print("Stored async closure result: \(result)")
    }
}

Task {
    await runExamples()
    
    print("=== KEY RULES ===")
    print("• Async closures MUST be called with 'await'")
    print("• The calling function must also be 'async'")
    print("• Use 'try await' for throwing async closures")
    print("• Async closures can contain await calls inside them")
    print("• Use @escaping if the closure might be called after the function returns")
    
    exit(0)
}

RunLoop.main.run()
