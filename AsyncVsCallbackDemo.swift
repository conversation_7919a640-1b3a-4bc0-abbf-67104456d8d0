import Foundation

print("=== BOTH PATTERNS ARE ASYNCHRONOUS ===\n")

// CALLBACK PATTERN: Asynchronous via completion handlers
func fetchDataCallback(completion: @escaping (String) -> Void) {
    print("  Callback: Function starts")
    
    // This is ASYNCHRONOUS work
    DispatchQueue.global().asyncAfter(deadline: .now() + 1) {
        print("  Callback: Async work completed")
        completion("Data from callback")
    }
    
    print("  Callback: Function returns immediately (async work continues)")
}

// ASYNC/AWAIT PATTERN: Asynchronous via suspension
func fetchDataAsync() async -> String {
    print("  Async: Function starts")
    
    // This is ALSO asynchronous, but structured
    let result = await withCheckedContinuation { continuation in
        DispatchQueue.global().asyncAfter(deadline: .now() + 1) {
            print("  Async: Async work completed")
            continuation.resume(returning: "Data from async")
        }
    }
    
    print("  Async: Function resumes and returns")
    return result
}

// MIXED PATTERN: Two asynchronous mechanisms at once
func fetchDataMixed(completion: @escaping (String) -> Void) async -> String {
    print("  Mixed: Function starts")
    
    // Async mechanism 1: Completion handler
    DispatchQueue.global().asyncAfter(deadline: .now() + 1) {
        print("  Mixed: Completion handler called")
        completion("Data from completion")
    }
    
    // Async mechanism 2: Async/await return
    let result = await withCheckedContinuation { continuation in
        DispatchQueue.global().asyncAfter(deadline: .now() + 2) {
            print("  Mixed: Async return ready")
            continuation.resume(returning: "Data from async return")
        }
    }
    
    print("  Mixed: Function returns")
    return result
}

func demonstratePatterns() async {
    print("1. CALLBACK PATTERN (Asynchronous):")
    print("Before callback")
    fetchDataCallback { data in
        print("Got callback data: \(data)")
    }
    print("After callback (but callback hasn't run yet)")
    try? await Task.sleep(nanoseconds: 1_500_000_000) // Wait to see callback
    print()
    
    print("2. ASYNC/AWAIT PATTERN (Also Asynchronous):")
    print("Before async")
    let asyncData = await fetchDataAsync()
    print("Got async data: \(asyncData)")
    print("After async")
    print()
    
    print("3. MIXED PATTERN (Confusing!):")
    print("Before mixed")
    let mixedData = await fetchDataMixed { completionData in
        print("Got completion data: \(completionData)")
    }
    print("Got async return data: \(mixedData)")
    print("After mixed")
    print()
}

Task {
    await demonstratePatterns()
    
    print("=== KEY INSIGHT ===")
    print("• Both callbacks AND async/await are asynchronous")
    print("• The problem is having TWO async mechanisms in one function")
    print("• This creates race conditions and timing confusion")
    print("• Pick ONE async pattern per function")
    
    exit(0)
}

RunLoop.main.run()
