import Foundation

// EXAMPLE 1: What we WANT to do but CAN'T
func getUserName() -> String {
    print("1. getUserName() starts")
    
    // This is what we WISH we could do, but it doesn't work
    fetchUserFromServer { userName in
        print("4. Inside closure - got userName: \(userName)")
        // ❌ This return doesn't work! It returns from the CLOSURE, not from getUserName()
        // return userName  // This would be a compile error
    }
    
    print("2. getUserName() is about to end")
    // ❌ We have no userName to return here! The closure hasn't run yet.
    return "???" // We're forced to return something, but we don't have the real data
    
    print("3. This line never executes because we already returned")
}

// EXAMPLE 2: The callback-based function (simulates network request)
func fetchUserFromServer(completion: @escaping (String) -> Void) {
    print("   fetchUserFromServer starts network request...")
    
    // Simulate network delay with DispatchQueue
    DispatchQueue.global().asyncAfter(deadline: .now() + 2) {
        print("   Network request completed!")
        completion("<PERSON> Doe") // This calls our closure after 2 seconds
    }
    
    print("   fetchUserFromServer returns immediately (before network finishes)")
}

// EXAMPLE 3: What actually happens when you run it
func demonstrateTheProblem() {
    print("=== DEMONSTRATING THE PROBLEM ===")
    
    let result = getUserName()
    print("5. Got result immediately: '\(result)'")
    print("6. But this is NOT the real user name!")
    
    print("\nWaiting for the closure to actually run...")
    Thread.sleep(forTimeInterval: 3) // Wait to see the closure execute
    print("7. Now the closure has run, but it's too late!\n")
}

// EXAMPLE 4: The correct way using callbacks
func getUserNameCorrectly(completion: @escaping (String) -> Void) {
    print("1. getUserNameCorrectly() starts")
    
    fetchUserFromServer { userName in
        print("3. Inside closure - got userName: \(userName)")
        // ✅ Instead of returning, we call the completion handler
        completion(userName)
    }
    
    print("2. getUserNameCorrectly() ends (but work continues in background)")
    // ✅ No return statement needed - we use the completion pattern
}

func demonstrateCorrectWay() {
    print("=== DEMONSTRATING THE CORRECT WAY ===")
    
    getUserNameCorrectly { userName in
        print("4. Finally got the real user name: '\(userName)'")
        print("5. Now we can actually use it!")
    }
    
    print("This prints immediately, before we get the user name")
    Thread.sleep(forTimeInterval: 3) // Wait to see it work
    print("")
}

// EXAMPLE 5: Compare with async/await (the modern solution)
func getUserNameAsync() async -> String {
    print("1. getUserNameAsync() starts")
    
    // Simulate the async version of fetchUserFromServer
    let userName = await withCheckedContinuation { continuation in
        fetchUserFromServer { userName in
            continuation.resume(returning: userName)
        }
    }
    
    print("2. Got userName: \(userName)")
    // ✅ Now we CAN return it because async/await handles the timing for us
    return userName
}

func demonstrateAsyncAwait() async {
    print("=== DEMONSTRATING ASYNC/AWAIT SOLUTION ===")
    
    let userName = await getUserNameAsync()
    print("3. Got result: '\(userName)'")
    print("4. This is the real user name, and we got it in the right order!\n")
}

// Run all examples
func runAllExamples() async {
    demonstrateTheProblem()
    demonstrateCorrectWay()
    await demonstrateAsyncAwait()
    
    print("=== SUMMARY ===")
    print("• Closures execute AFTER the function returns")
    print("• You can't return from a closure to the original caller")
    print("• Use completion handlers or async/await instead")
}

// Uncomment this line to run the examples:
// Task { await runAllExamples() }
