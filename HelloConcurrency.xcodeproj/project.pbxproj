// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		971971092BB3209D0098AFD8 /* AsyncStreams.swift in Sources */ = {isa = PBXBuildFile; fileRef = 971971082BB3209D0098AFD8 /* AsyncStreams.swift */; };
		97B363DF28BE36AF00A3A03B /* HelloConcurrencyAppApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97B363DE28BE36AF00A3A03B /* HelloConcurrencyAppApp.swift */; };
		97B363E128BE36AF00A3A03B /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97B363E028BE36AF00A3A03B /* ContentView.swift */; };
		97B363E328BE36B000A3A03B /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97B363E228BE36B000A3A03B /* Assets.xcassets */; };
		97B363E628BE36B000A3A03B /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97B363E528BE36B000A3A03B /* Preview Assets.xcassets */; };
		97B363F128BE36B000A3A03B /* HelloConcurrencyAppTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97B363F028BE36B000A3A03B /* HelloConcurrencyAppTests.swift */; };
		97B363FB28BE36B000A3A03B /* HelloConcurrencyAppUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97B363FA28BE36B000A3A03B /* HelloConcurrencyAppUITests.swift */; };
		97B363FD28BE36B000A3A03B /* HelloConcurrencyAppUITestsLaunchTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97B363FC28BE36B000A3A03B /* HelloConcurrencyAppUITestsLaunchTests.swift */; };
		97B3640728BE36B800A3A03B /* Refactoring.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97B363D628BE323500A3A03B /* Refactoring.swift */; };
		97B3640828BE36B800A3A03B /* Introduction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97D23E5A28BE0D00000904E4 /* Introduction.swift */; };
		97B3640928BE36D300A3A03B /* Cast.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97D23E5228BE0CD9000904E4 /* Cast.swift */; };
		97B3640A28BE36D600A3A03B /* Movie.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97D23E5428BE0CD9000904E4 /* Movie.swift */; };
		97B3640B28BE36D600A3A03B /* Person.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97D23E5328BE0CD9000904E4 /* Person.swift */; };
		97B3640C28BE36D600A3A03B /* Crew.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97D23E5128BE0CD9000904E4 /* Crew.swift */; };
		97B3640F28BE3A0600A3A03B /* Actors.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97B3640E28BE3A0600A3A03B /* Actors.swift */; };
		97B3641128BE3FEE00A3A03B /* Orchestrate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 97B3641028BE3FEE00A3A03B /* Orchestrate.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		97B363ED28BE36B000A3A03B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97D23E2F28BE0654000904E4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 97B363DB28BE36AF00A3A03B;
			remoteInfo = HelloConcurrencyApp;
		};
		97B363F728BE36B000A3A03B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97D23E2F28BE0654000904E4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 97B363DB28BE36AF00A3A03B;
			remoteInfo = HelloConcurrencyApp;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		971971082BB3209D0098AFD8 /* AsyncStreams.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AsyncStreams.swift; sourceTree = "<group>"; };
		97B363D628BE323500A3A03B /* Refactoring.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Refactoring.swift; sourceTree = "<group>"; };
		97B363DC28BE36AF00A3A03B /* HelloConcurrencyApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = HelloConcurrencyApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97B363DE28BE36AF00A3A03B /* HelloConcurrencyAppApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HelloConcurrencyAppApp.swift; sourceTree = "<group>"; };
		97B363E028BE36AF00A3A03B /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		97B363E228BE36B000A3A03B /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97B363E528BE36B000A3A03B /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		97B363E728BE36B000A3A03B /* HelloConcurrencyApp.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = HelloConcurrencyApp.entitlements; sourceTree = "<group>"; };
		97B363EC28BE36B000A3A03B /* HelloConcurrencyAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = HelloConcurrencyAppTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		97B363F028BE36B000A3A03B /* HelloConcurrencyAppTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HelloConcurrencyAppTests.swift; sourceTree = "<group>"; };
		97B363F628BE36B000A3A03B /* HelloConcurrencyAppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = HelloConcurrencyAppUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		97B363FA28BE36B000A3A03B /* HelloConcurrencyAppUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HelloConcurrencyAppUITests.swift; sourceTree = "<group>"; };
		97B363FC28BE36B000A3A03B /* HelloConcurrencyAppUITestsLaunchTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HelloConcurrencyAppUITestsLaunchTests.swift; sourceTree = "<group>"; };
		97B3640E28BE3A0600A3A03B /* Actors.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Actors.swift; sourceTree = "<group>"; };
		97B3641028BE3FEE00A3A03B /* Orchestrate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Orchestrate.swift; sourceTree = "<group>"; };
		97D23E5128BE0CD9000904E4 /* Crew.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Crew.swift; sourceTree = "<group>"; };
		97D23E5228BE0CD9000904E4 /* Cast.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Cast.swift; sourceTree = "<group>"; };
		97D23E5328BE0CD9000904E4 /* Person.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Person.swift; sourceTree = "<group>"; };
		97D23E5428BE0CD9000904E4 /* Movie.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Movie.swift; sourceTree = "<group>"; };
		97D23E5A28BE0D00000904E4 /* Introduction.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Introduction.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		97B363D928BE36AF00A3A03B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97B363E928BE36B000A3A03B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97B363F328BE36B000A3A03B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		97B363DD28BE36AF00A3A03B /* HelloConcurrencyApp */ = {
			isa = PBXGroup;
			children = (
				97B3640D28BE39EC00A3A03B /* Samples */,
				97D23E5928BE0CE8000904E4 /* Models */,
				97B363DE28BE36AF00A3A03B /* HelloConcurrencyAppApp.swift */,
				97B363E028BE36AF00A3A03B /* ContentView.swift */,
				97B363E228BE36B000A3A03B /* Assets.xcassets */,
				97B363E728BE36B000A3A03B /* HelloConcurrencyApp.entitlements */,
				97B363E428BE36B000A3A03B /* Preview Content */,
			);
			path = HelloConcurrencyApp;
			sourceTree = "<group>";
		};
		97B363E428BE36B000A3A03B /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				97B363E528BE36B000A3A03B /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		97B363EF28BE36B000A3A03B /* HelloConcurrencyAppTests */ = {
			isa = PBXGroup;
			children = (
				97B363F028BE36B000A3A03B /* HelloConcurrencyAppTests.swift */,
			);
			path = HelloConcurrencyAppTests;
			sourceTree = "<group>";
		};
		97B363F928BE36B000A3A03B /* HelloConcurrencyAppUITests */ = {
			isa = PBXGroup;
			children = (
				97B363FA28BE36B000A3A03B /* HelloConcurrencyAppUITests.swift */,
				97B363FC28BE36B000A3A03B /* HelloConcurrencyAppUITestsLaunchTests.swift */,
			);
			path = HelloConcurrencyAppUITests;
			sourceTree = "<group>";
		};
		97B3640D28BE39EC00A3A03B /* Samples */ = {
			isa = PBXGroup;
			children = (
				97B363D628BE323500A3A03B /* Refactoring.swift */,
				97D23E5A28BE0D00000904E4 /* Introduction.swift */,
				97B3640E28BE3A0600A3A03B /* Actors.swift */,
				97B3641028BE3FEE00A3A03B /* Orchestrate.swift */,
				971971082BB3209D0098AFD8 /* AsyncStreams.swift */,
			);
			path = Samples;
			sourceTree = "<group>";
		};
		97D23E2E28BE0654000904E4 = {
			isa = PBXGroup;
			children = (
				97B363DD28BE36AF00A3A03B /* HelloConcurrencyApp */,
				97B363EF28BE36B000A3A03B /* HelloConcurrencyAppTests */,
				97B363F928BE36B000A3A03B /* HelloConcurrencyAppUITests */,
				97D23E3828BE0654000904E4 /* Products */,
			);
			sourceTree = "<group>";
		};
		97D23E3828BE0654000904E4 /* Products */ = {
			isa = PBXGroup;
			children = (
				97B363DC28BE36AF00A3A03B /* HelloConcurrencyApp.app */,
				97B363EC28BE36B000A3A03B /* HelloConcurrencyAppTests.xctest */,
				97B363F628BE36B000A3A03B /* HelloConcurrencyAppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97D23E5928BE0CE8000904E4 /* Models */ = {
			isa = PBXGroup;
			children = (
				97D23E5228BE0CD9000904E4 /* Cast.swift */,
				97D23E5128BE0CD9000904E4 /* Crew.swift */,
				97D23E5428BE0CD9000904E4 /* Movie.swift */,
				97D23E5328BE0CD9000904E4 /* Person.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		97B363DB28BE36AF00A3A03B /* HelloConcurrencyApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97B363FE28BE36B000A3A03B /* Build configuration list for PBXNativeTarget "HelloConcurrencyApp" */;
			buildPhases = (
				97B363D828BE36AF00A3A03B /* Sources */,
				97B363D928BE36AF00A3A03B /* Frameworks */,
				97B363DA28BE36AF00A3A03B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = HelloConcurrencyApp;
			productName = HelloConcurrencyApp;
			productReference = 97B363DC28BE36AF00A3A03B /* HelloConcurrencyApp.app */;
			productType = "com.apple.product-type.application";
		};
		97B363EB28BE36B000A3A03B /* HelloConcurrencyAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97B3640128BE36B000A3A03B /* Build configuration list for PBXNativeTarget "HelloConcurrencyAppTests" */;
			buildPhases = (
				97B363E828BE36B000A3A03B /* Sources */,
				97B363E928BE36B000A3A03B /* Frameworks */,
				97B363EA28BE36B000A3A03B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				97B363EE28BE36B000A3A03B /* PBXTargetDependency */,
			);
			name = HelloConcurrencyAppTests;
			productName = HelloConcurrencyAppTests;
			productReference = 97B363EC28BE36B000A3A03B /* HelloConcurrencyAppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		97B363F528BE36B000A3A03B /* HelloConcurrencyAppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97B3640428BE36B000A3A03B /* Build configuration list for PBXNativeTarget "HelloConcurrencyAppUITests" */;
			buildPhases = (
				97B363F228BE36B000A3A03B /* Sources */,
				97B363F328BE36B000A3A03B /* Frameworks */,
				97B363F428BE36B000A3A03B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				97B363F828BE36B000A3A03B /* PBXTargetDependency */,
			);
			name = HelloConcurrencyAppUITests;
			productName = HelloConcurrencyAppUITests;
			productReference = 97B363F628BE36B000A3A03B /* HelloConcurrencyAppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97D23E2F28BE0654000904E4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1340;
				LastUpgradeCheck = 1340;
				TargetAttributes = {
					97B363DB28BE36AF00A3A03B = {
						CreatedOnToolsVersion = 13.4.1;
					};
					97B363EB28BE36B000A3A03B = {
						CreatedOnToolsVersion = 13.4.1;
						TestTargetID = 97B363DB28BE36AF00A3A03B;
					};
					97B363F528BE36B000A3A03B = {
						CreatedOnToolsVersion = 13.4.1;
						TestTargetID = 97B363DB28BE36AF00A3A03B;
					};
				};
			};
			buildConfigurationList = 97D23E3228BE0654000904E4 /* Build configuration list for PBXProject "HelloConcurrency" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 97D23E2E28BE0654000904E4;
			productRefGroup = 97D23E3828BE0654000904E4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97B363DB28BE36AF00A3A03B /* HelloConcurrencyApp */,
				97B363EB28BE36B000A3A03B /* HelloConcurrencyAppTests */,
				97B363F528BE36B000A3A03B /* HelloConcurrencyAppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		97B363DA28BE36AF00A3A03B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97B363E628BE36B000A3A03B /* Preview Assets.xcassets in Resources */,
				97B363E328BE36B000A3A03B /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97B363EA28BE36B000A3A03B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97B363F428BE36B000A3A03B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		97B363D828BE36AF00A3A03B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				971971092BB3209D0098AFD8 /* AsyncStreams.swift in Sources */,
				97B3640728BE36B800A3A03B /* Refactoring.swift in Sources */,
				97B363E128BE36AF00A3A03B /* ContentView.swift in Sources */,
				97B3640A28BE36D600A3A03B /* Movie.swift in Sources */,
				97B3640928BE36D300A3A03B /* Cast.swift in Sources */,
				97B3641128BE3FEE00A3A03B /* Orchestrate.swift in Sources */,
				97B3640B28BE36D600A3A03B /* Person.swift in Sources */,
				97B363DF28BE36AF00A3A03B /* HelloConcurrencyAppApp.swift in Sources */,
				97B3640F28BE3A0600A3A03B /* Actors.swift in Sources */,
				97B3640C28BE36D600A3A03B /* Crew.swift in Sources */,
				97B3640828BE36B800A3A03B /* Introduction.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97B363E828BE36B000A3A03B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97B363F128BE36B000A3A03B /* HelloConcurrencyAppTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97B363F228BE36B000A3A03B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97B363FB28BE36B000A3A03B /* HelloConcurrencyAppUITests.swift in Sources */,
				97B363FD28BE36B000A3A03B /* HelloConcurrencyAppUITestsLaunchTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		97B363EE28BE36B000A3A03B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 97B363DB28BE36AF00A3A03B /* HelloConcurrencyApp */;
			targetProxy = 97B363ED28BE36B000A3A03B /* PBXContainerItemProxy */;
		};
		97B363F828BE36B000A3A03B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 97B363DB28BE36AF00A3A03B /* HelloConcurrencyApp */;
			targetProxy = 97B363F728BE36B000A3A03B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		97B363FF28BE36B000A3A03B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = HelloConcurrencyApp/HelloConcurrencyApp.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"HelloConcurrencyApp/Preview Content\"";
				DEVELOPMENT_TEAM = 4JMM8JMG3H;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 13;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.donnywals.HelloConcurrencyApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		97B3640028BE36B000A3A03B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = HelloConcurrencyApp/HelloConcurrencyApp.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"HelloConcurrencyApp/Preview Content\"";
				DEVELOPMENT_TEAM = 4JMM8JMG3H;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 13;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.donnywals.HelloConcurrencyApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		97B3640228BE36B000A3A03B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4JMM8JMG3H;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.3;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.donnywals.HelloConcurrencyAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/HelloConcurrencyApp.app/Contents/MacOS/HelloConcurrencyApp";
			};
			name = Debug;
		};
		97B3640328BE36B000A3A03B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4JMM8JMG3H;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.3;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.donnywals.HelloConcurrencyAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/HelloConcurrencyApp.app/Contents/MacOS/HelloConcurrencyApp";
			};
			name = Release;
		};
		97B3640528BE36B000A3A03B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4JMM8JMG3H;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.donnywals.HelloConcurrencyAppUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = HelloConcurrencyApp;
			};
			name = Debug;
		};
		97B3640628BE36B000A3A03B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4JMM8JMG3H;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.donnywals.HelloConcurrencyAppUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = HelloConcurrencyApp;
			};
			name = Release;
		};
		97D23E3C28BE0654000904E4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.3;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		97D23E3D28BE0654000904E4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.3;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		97B363FE28BE36B000A3A03B /* Build configuration list for PBXNativeTarget "HelloConcurrencyApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97B363FF28BE36B000A3A03B /* Debug */,
				97B3640028BE36B000A3A03B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97B3640128BE36B000A3A03B /* Build configuration list for PBXNativeTarget "HelloConcurrencyAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97B3640228BE36B000A3A03B /* Debug */,
				97B3640328BE36B000A3A03B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97B3640428BE36B000A3A03B /* Build configuration list for PBXNativeTarget "HelloConcurrencyAppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97B3640528BE36B000A3A03B /* Debug */,
				97B3640628BE36B000A3A03B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97D23E3228BE0654000904E4 /* Build configuration list for PBXProject "HelloConcurrency" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97D23E3C28BE0654000904E4 /* Debug */,
				97D23E3D28BE0654000904E4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97D23E2F28BE0654000904E4 /* Project object */;
}
