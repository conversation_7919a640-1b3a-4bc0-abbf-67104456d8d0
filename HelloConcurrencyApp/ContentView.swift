//
//  ContentView.swift
//  HelloConcurrencyApp
//
//  Created by <PERSON><PERSON> on 30/08/2022.
//

import SwiftUI

struct ContentView: View {
    @State var isLoading = false
    var body: some View {
        VStack {
            Text("Hello, world!")
            if isLoading {
                ProgressView()
            }
        }
            .padding()
            .task {
                do {
//                    try await Introduction.run()
//                    try await Refactoring.run()
                  try await AsyncStreams.run()
//                    try await Actors.run()
//                    try await Orchestrate.run()
                } catch {
                    // ...
                }
            }
    }
}
