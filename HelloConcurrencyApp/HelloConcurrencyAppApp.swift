//
//  HelloConcurrencyAppApp.swift
//  HelloConcurrencyApp
//
//  Created by <PERSON><PERSON> on 30/08/2022.
//

import SwiftUI

@main
struct HelloConcurrencyAppApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}

func sample(_ closure: () async throws -> Void) async throws {
    try await closure()
}

func xsample(_ closure: () async throws -> Void) async throws {
    // no-op
}

func syncSample(_ closure: () -> Void) {
    closure()
}

func xsyncSample(_ closure: () -> Void) {
    // no-op
}
