//
//  Actors.swift
//  HelloConcurrencyApp
//
//  Created by <PERSON><PERSON> on 30/08/2022.
//

import Foundation

struct Actors {
    static func run() async throws {
        /**
         Data races can sneak up on us when we use an unsafe implementation of our code
         */
        try await xsample {
            let cache = DictionaryCache()
            
            DispatchQueue.concurrentPerform(iterations: 50) { iteration in
                if iteration % 2 == 0 {
                    print(cache.getValue(forKey: UUID().uuidString) ?? 0)
                } else {
                    cache.writeValue(iteration, forKey: UUID().uuidString)
                }
            }
        }
        
        /**
         We can use actors to prevent data races because they synchronize access to their mutable state
         It's important to realize that using an actor makes accessing state and calling functions on the actor async implicitly
         Internally, the actor doesn't `await` its own state or method. But outside of the actor we do have to `await`
         When you `await` something within an actor, this isn't blocking. The actor will suspend your function and continue pocessing its mailbox.
         This means that certain state can be different before and after an await. It's still thread safe, but your assumptions might no longer hold.
         */
        try await xsample {
            let cache = DictionaryCache2()
            
            DispatchQueue.concurrentPerform(iterations: 50) { iteration in
                Task.detached {
                    let id = UUID().uuidString
                    try await cache.writeValue(iteration, forKey: id)
                    await print(cache.getValue(forKey: id) ?? 0)
                }
            }
        }
        
        /**
         We can leverage global actors to run code on a specific actor. The main actor is a global actor.
         */
        try await xsample {
            Task.detached { @MainActor in
                print("@MainActor: ", Thread.isMainThread)
            }
            
            Task.detached {
                print("No actor: ", Thread.isMainThread)
            }
        }
        
        /**
         We can also mark methods and properties with a main actor.
         It's also possible to opt out of actor isolation using the `nonisolated` keyword.
         */
        try await xsample { @MainActor in
            let vm = ViewModel()
            print(vm.myState)
            print(vm.normalState)
        }
    }
}


class ViewModel: ObservableObject {
    @MainActor @Published var myState = 10
    @MainActor @Published var normalState = 10
    
    func getDisplayState() async -> String {
        return "State: \(await myState)"
    }
}

actor DictionaryCache2 {
    private var cache: [String: Int] = [:]
    
    func writeValue(_ value: Int, forKey key: String) async throws {
        print("---")
        print("There are \(cache.keys.count) values in the cache")
        cache[key] = value
        print("There are \(cache.keys.count) values in the cache")
        print("---")
    }
    
    func getValue(forKey key: String) -> Int? {
        return cache[key]
    }
    
    func awaitSomething() async throws {
        print("await before", cache.keys.count)
        try await Task.sleep(nanoseconds: 100)
        print("await after", cache.keys.count)
    }
}

class DictionaryCache {
    private var cache: [String: Int] = [:]
    
    func writeValue(_ value: Int, forKey key: String) {
        cache[key] = value
    }
    
    func getValue(forKey key: String) -> Int? {
        return cache[key]
    }
}
