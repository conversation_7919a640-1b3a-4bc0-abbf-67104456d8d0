//
//  AsyncStreams.swift
//  HelloConcurrencyApp
//
//  Created by <PERSON><PERSON> on 26/03/2024.
//

import Foundation

// step 1: set up a view with a ViewModel that has a deinit
// step 2: start building a UI that can
//            - dismiss the sheet
//            - cancel our iteration task
//            - break out of the for loop we'll write
// step 3: implement the view model and the socket object

struct AsyncStreams {
  @MainActor
  static var viewModel: ViewModelExample? = ViewModelExample()
  
  @MainActor
  static func run() async throws {
    var theTask = Task {
      guard let stream = viewModel?.stream else {
        return
      }
      
      var msgCount = 0
      do {
        for try await message in stream {
          print("message", message)
//          msgCount += 1
//          if msgCount == 3 {
//            break
//          }
        }
      } catch {
        // handle error
      }
      
      print("this will be printed once the stream ends")
    }
    
    Task {
      try await Task.sleep(for: .seconds(5))
      //try await viewModel?.stream.cancel()
      viewModel = nil
      //theTask.cancel()
    }
  }
}

class ViewModelExample {
  let stream: SocketStream
  
  init() {
    let url = URL(string: "ws://127.0.0.1:8080")!
    let socketConnection = URLSession.shared.webSocketTask(with: url)
    self.stream = SocketStream(task: socketConnection)
  }
  
  deinit {
    print("deinit the viewmodel")
    Task { [stream] in
      try await stream.cancel()
    }
  }
}

typealias WebSocketStream = AsyncThrowingStream<URLSessionWebSocketTask.Message, Error>

class SocketStream: AsyncSequence {
  typealias AsyncIterator = WebSocketStream.Iterator
  typealias Element = URLSessionWebSocketTask.Message
  
  private var continuation: WebSocketStream.Continuation
  private let task: URLSessionWebSocketTask
  
  private let stream: WebSocketStream
  
  init(task: URLSessionWebSocketTask) {
    self.task = task
    task.resume()
    
    let pair = WebSocketStream.makeStream()
    continuation = pair.continuation
    stream = pair.stream
    
    continuation.onTermination = { [weak self] _ in
      Task { [weak self] in
        try await self?.cancel()
      }
    }
    
    Task {
      var isAlive = true
      
      while isAlive && task.closeCode == .invalid {
        do {
          let value = try await task.receive()
          pair.continuation.yield(value)
        } catch {
          pair.continuation.finish(throwing: error)
          isAlive = false
        }
      }
    }
  }
  
  deinit {
    print("the socket stream deinits")
    continuation.finish()
  }
  
  func makeAsyncIterator() -> AsyncIterator {
    return stream.makeAsyncIterator()
  }
  
  func cancel() async throws {
    print("canceling")
    task.cancel(with: .goingAway, reason: nil)
    continuation.finish()
  }
}
