import Foundation

struct Introduction {
    @MainActor
    static func run() async throws {
        /**
         A traditional callback based API uses closures to communicate the result of an operation back to the caller.
         This is fine if you know how it works but it can be pretty confusing; the result is only available within the closure.
         More importantly, the scope that started the work is long gone; you can't just return the result from some method.
         */
        try await xsample {
            fetchMovies { movies in
                print("we can use movies now")
                print(movies)
                // we can't do this of course
                // return movies
            }
        }
        
        /**
         The async await version of the same code is much nicer to read; it reads a lot like async code.
         */
        try await xsample {
            // Suspend the current function; free the thread for other work.
            let movies = try await fetchMoviesAsync()
            print("we can use movies now")
            print(movies)
            
            // we could do this
            // return movies
        }
        
        /**
         We can't call async functions from a synchronous context, we have to "go async" before we're allowed to call an asynchronous function
         */
        xsyncSample {
            // this is not allowed
            // let movies = try await fetchMoviesAsync()
            
            Task {
                // but this is
                let movies = try await fetchMoviesAsync()
                print(movies)
            }
        }
        
        /**
         Work in Swift Concurrency is modelled via Tasks rather than threads. This means that we don't create or manage threads; <PERSON> does this for us.
         When we want to create new tasks to create an async context, or to allow code to run in parallel, we can either create an unstructured task or a detached task.
         A detached task creates a new execution context, this means that it inherits nothing from the place it's created in, and it will typically be run away from the main thread.
         This is especially true in apps; a command line tool behaves slightly different.
         An unstructured task does inherit context. This means that certain things like the actor it's run on carry over from the place where the task is created.
         It's not created as a child task though (more on child tasks later)
         */
        try await sample {
            print("Before all isMain: ", Thread.isMainThread)
            
            let task = Task.detached {
                print("Detached pre-await isMain: ", Thread.isMainThread)
                let _ = try await fetchMoviesAsync()
                print("Detached post-await isMain: ", Thread.isMainThread)
            }
                
            let result = try await task.value
            
            try await Task {
                print("Unstructured pre-await isMain: ", Thread.isMainThread)
                let _ = try await fetchMoviesAsync()
                print("Unstructured post-await isMain: ", Thread.isMainThread)
            }.value
        }
        
        /**
         We can force code to run on the main thread with `MainActor.run`
         */
        try await xsample {
            print("Before all isMain: ", Thread.isMainThread)
            
            try await Task.detached {
                await MainActor.run {
                    print("Detached pre-await isMain: ", Thread.isMainThread)
                }
                let _ = try await fetchMoviesAsync()
                await MainActor.run {
                    print("Detached post-await isMain: ", Thread.isMainThread)
                }
            }.value
            
            try await Task {
                print("Unstructured pre-await isMain: ", Thread.isMainThread)
                let _ = try await fetchMoviesAsync()
                print("Unstructured post-await isMain: ", Thread.isMainThread)
            }.value
        }
        
        /**
         If we make both the detached and the regular task run in parallel we can see that things change ever so slightly.
         We can see that the unstructured task now starts off away from the main thread because our task doesn't _have_ to run on the main thread.
         Instead, the main thread is kept free so that our normal execution can resume while both of our tasks run in parallel.
         We'll learn more about aysync lets later.
         */
        try await xsample {
            print("Before all isMain: ", Thread.isMainThread)
            
            Task.detached {
                print("Detached pre-await isMain: ", Thread.isMainThread)
                let _ = try await fetchMoviesAsync()
                print("Detached post-await isMain: ", Thread.isMainThread)
            }
            
            Task {
                print("Unstructured pre-await isMain: ", Thread.isMainThread)
                let _ = try await fetchMoviesAsync()
                print("Unstructured post-await isMain: ", Thread.isMainThread)
            }
            
            print("After all isMain: ", Thread.isMainThread)
        }
    }
    
    static func fetchMovies(page: Int = 1, _ completion: @escaping (Result<[Movie], Error>) -> Void) {
        let url = URL(string: "http://127.0.0.1:8080/\(page).json")!
        URLSession.shared.dataTask(with: url) { data, response, error in
            guard error == nil else {
                completion(.failure(error!))
                return
            }
            
            do {
                let decoder = JSONDecoder()
                let movies = try decoder.decode([Movie].self, from: data!)
                completion(.success(movies))
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    /**
     An async function declaration has the `async` keyword after its argument list. If the function can throw, the `throws` keyword must come after the `async` keyword.
     An async function can only be called from an async context.
     */
    static func fetchMoviesAsync() async throws -> [Movie] {
      print(Thread.isMainThread)
        let url = URL(string: "http://localhost:8080/1.json")!
        let (data, response) = try await URLSession.shared.data(from: url)
        let decoder = JSONDecoder()
        return try decoder.decode([Movie].self, from: data)
    }
}
