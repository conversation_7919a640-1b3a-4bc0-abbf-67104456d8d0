//
//  Orchestrate.swift
//  HelloConcurrencyApp
//
//  Created by <PERSON><PERSON> on 30/08/2022.
//

import Foundation

struct Orchestrate {
    static func run() async throws {
        /**
         We can run a bunch of tasks in parallel with task groups
         */
        try await xsample {
            let datas = try await runTaskGroup()
            print(datas.count)
        }
        
        /**
         We can use async let to start working on a given task without immediately awaiting its result
         */
        try await xsample {
            // we can call our async function with try await to create an `async let`
            async let one = fetchPage(1)
            async let two = fetchPage(2)
            async let three = fetchPage(3)
            
            // we can `try await` the async lets at the point where we need them.
            try await print(one.count, two.count, three.count)
            
            // reading the same value after awaiting it once still requires us to await the async let
            try await print(one)
            
            // we can assign the awaited result to a property to prevent this
            let resolvedOne = try await one
        }
    }
    
    static func fetchPage(_ page: Int) async throws -> [Movie] {
        let url = URL(string: "http://localhost:8080/\(page).json")!
        let (data, response) = try await URLSession.shared.data(from: url)
        let decoder = JSONDecoder()
        return try decoder.decode([Movie].self, from: data)
    }
    
    static func runTaskGroup() async throws -> [Data] {
        // A group is creates as a throwing (or non throwing) task group.
        // We pass a type of output and a closure that will receive a task group
        return try await withThrowingTaskGroup(of: Data.self) { group in
            // we can add tasks in a loop
            for i in (1...20) {
                let url = URL(string: "http://127.0.0.1:8080/\(i).json")!
                group.addTask {
                    // a task can return data or throw an error
                    let (data, _) = try await URLSession.shared.data(from: url)
                    return data
                }
            }
            
            // By default the task will complete when all child tasks are done.
            // This can happen through succesful completion or cancellation.
            // Whenever we throw an error from the task group closure, all running tasks will get cancelled.
            
            var datas = [Data]()
            // We can grab the results of a task group by looping over it.
            // Results are not returned in order.
            while let result = await group.nextResult() {
                guard case let .success(success) = result else {
                    return datas
                }
                
                datas.append(success)
            }
//            for try await value in group {
//                datas.append(value)
//            }
            
            // The return value of the task group is what we can return from our function.
            return datas
        }
    }
}
