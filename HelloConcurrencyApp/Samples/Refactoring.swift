//
//  Refactoring.swift
//  HelloConcurrency
//
//  Created by <PERSON><PERSON> on 30/08/2022.
//

import Foundation
import Combine

struct Refactoring {
    static func run() async throws {
        /**
         We can leverage continuations to convert existing code into async / await. All methods below come from callback based code.
        */
        try await xsample {
            let movies = try await fetchMovies()
            print(movies)
        }
        
        try await xsample {
            let movies = try await checkedBadExample()
            print(movies)
        }
        
        try await xsample {
            let movies = try await unsafeBadExample()
            print(movies)
        }
        
        /**
         To bridge Combine based code we should take a look at async sequences. Combine based code doesn't lend itself to continuation based refactoring very well.
         Looping over an async sequence is very similar to looping over a regular sequence.
         */
        try await xsample {
            let url = URL(string: "https://donnywals.com")!
            
            for try await line in url.lines {
                if !line.starts(with: "<") {
                    continue
                } else {
                    print(line)
                }
            }
        }
        
        /**
         We can break out of an async loop as well.
         */
        try await xsample {
            let url = URL(string: "https://donnywals.com")!
            
            for try await line in url.lines {
                if !line.starts(with: "<") {
                    break
                } else {
                    print(line)
                }
            }
        }
        
        /**
         And we can return from an async loop as well. This is helpful for Combine code.
         */
        try await xsample {
            let body = try await findBody()
            print(body)
        }
        
        /**
         When looping over an async sequence, we don't execute code past the loop until the loop is done. Just like with a normal loop.
         */
        try await xsample {
            let url = URL(string: "https://donnywals.com")!
            
            for try await line in url.lines {
              print("one: \(line.count)")
            }
            
            let url2 = URL(string: "https://donnywals.com")!
            
            for try await line in url2.lines {
                print("two: \(line.count)")
            }
        }
        
        /**
         We can use tasks to run multiple loops in parallel. The lifecycle of these loops are... interesting.
         */
        try await xsample {
            Task {
                let url = URL(string: "https://donnywals.com")!
            
                for try await line in url.lines {
                    print("💚 one: \(Thread.current)")
                }
            }
            
            Task {
                let url2 = URL(string: "https://donnywals.com")!
            
                for try await line in url2.lines {
                    print("💜 two: \(Thread.current)")
                }
            }
        }
        
        /**
         We can return from a loop to bridge Combine based code.
         */
        try await xsample {
            let movies = try await fetchMoviesCombineBridge()
            print(movies)
        }
      
      try await sample {
        let publisher = PassthroughSubject<Int, Never>()
        
        var viewModel: SampleViewModel? = SampleViewModel(publisher: publisher.eraseToAnyPublisher())
        
        viewModel?.startLooping()
        
        try await Task.sleep(for: .seconds(1))
        publisher.send(Int.random(in: 0..<100))
        try await Task.sleep(for: .seconds(1))
        publisher.send(Int.random(in: 0..<100))
        try await Task.sleep(for: .seconds(1))
        publisher.send(Int.random(in: 0..<100))
        try await Task.sleep(for: .seconds(1))
        publisher.send(Int.random(in: 0..<100))
        
        viewModel = nil
        
        try await Task.sleep(for: .seconds(1))
        publisher.send(Int.random(in: 0..<100))
        try await Task.sleep(for: .seconds(1))
        publisher.send(Int.random(in: 0..<100))
        try await Task.sleep(for: .seconds(1))
        publisher.send(Int.random(in: 0..<100))
        
        publisher.send(completion: .finished)
      }
    }
    
    static func fetchMoviesCombineBridge() async throws -> [Movie] {
        let publisher = fetchMoviesCombine()
        
        for try await movies in publisher.values {
            return movies
        }
        
        fatalError("Expected movies to be returned or error to be thrown")
    }
    
    static func findBody() async throws -> String {
        let url = URL(string: "https://donnywals.com")!
        
        for try await line in url.lines {
            if line.contains("<body") {
                return line
            }
        }
        
        return ""
    }
    
    /**
     We can use continuations to build an async task that calls out to traditional code. Our method will be suspended until the continuation is called.
     We can either pass a result, return value, or error to the `resume` method.
     You should never call a `resume` method more than once, nor should you make the continuation outlive the scope of the closure it's passed to.
     We can leverage checked or unsafe continuations depending on how much help we need at runtime to ensure that we're not using continuations wrong.
     The Swift team recommends to use unsafe once you're certain your implementation is correct. There is a small overhead with checked continuations.
     */
    static func fetchMovies(page: Int = 1) async throws -> [Movie] {
        //return try await withUnsafeThrowingContinuation { continuation in
        return try await withCheckedThrowingContinuation { continuation in
            fetchMovies(page: page) { result in
                continuation.resume(with: result)
            }
        }
    }
    
    static func checkedBadExample() async throws -> [Movie] {
        return try await withCheckedThrowingContinuation { continuation in
            fetchMovies { result in
                // we're not supposed to resume more than once
                continuation.resume(with: result)
            }
        }
    }
    
    static func unsafeBadExample() async throws -> [Movie] {
        return try await withUnsafeThrowingContinuation { continuation in
            fetchMovies { result in
                do {
                    let movies = try result.get()
                    continuation.resume(returning: movies)
                } catch {
                    continuation.resume(throwing: error)
                }
                
                // we're not supposed to resume more than once
                //continuation.resume(with: result)
            }
        }
    }
    
    static func fetchMoviesCombine(page: Int = 1) -> AnyPublisher<[Movie], Error> {
        return Future { promise in
            fetchMovies { result in
                promise(result)
            }
        }.eraseToAnyPublisher()
    }
    
    static func fetchMovies(page: Int = 1, _ completion: @escaping (Result<[Movie], Error>) -> Void) {
        let url = URL(string: "http://127.0.0.1:8080/\(page).json")!
        URLSession.shared.dataTask(with: url) { data, response, error in
            guard error == nil else {
                completion(.failure(error!))
                return
            }
            
            do {
                let decoder = JSONDecoder()
                let movies = try decoder.decode([Movie].self, from: data!)
                completion(.success(movies))
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
}

class SampleViewModel {
  let publisher: AnyPublisher<Int, Never>
  var task: Task<Void, Never>?
  
  init(publisher: AnyPublisher<Int, Never>) {
    self.publisher = publisher
  }
  
  deinit {
    print("view model is gone")
    task?.cancel()
  }
  
  func startLooping() {
    task = Task { [weak self] in
      guard let publisher = self?.publisher else {
        return
      }
      
      print("task starts looping")
      for await value in publisher.values {
        print(value)
      }
      print("task ended looping")
    }
  }
}
