import Foundation

print("=== UNDERSTANDING SWIFT'S RESULT TYPE ===\n")

// WHAT IS RESULT?
// Result<Success, Failure> is an enum with two cases:
// - .success(Success) - contains the successful value
// - .failure(Failure) - contains the error

// EXAMPLE 1: Basic Result usage
func demonstrateBasicResult() {
    print("1. BASIC RESULT USAGE:")
    
    // Creating Results manually
    let successResult: Result<String, Error> = .success("Hello World")
    let failureResult: Result<String, Error> = .failure(NSError(domain: "TestError", code: 1))
    
    // Handling Results with switch
    switch successResult {
    case .success(let value):
        print("   Success: \(value)")
    case .failure(let error):
        print("   Error: \(error)")
    }
    
    switch failureResult {
    case .success(let value):
        print("   Success: \(value)")
    case .failure(let error):
        print("   Error: \(error)")
    }
    print()
}

// EXAMPLE 2: Result in callback-based APIs (like in your HelloConcurrency project)
func fetchMoviesWithResult(completion: @escaping (Result<[String], Error>) -> Void) {
    print("2. RESULT IN CALLBACK APIs:")
    
    // Simulate network request
    DispatchQueue.global().asyncAfter(deadline: .now() + 1) {
        let success = Bool.random()
        
        if success {
            // Success case - return data
            let movies = ["Movie 1", "Movie 2", "Movie 3"]
            completion(.success(movies))
        } else {
            // Failure case - return error
            let error = NSError(domain: "NetworkError", code: 404, userInfo: [NSLocalizedDescriptionKey: "Movies not found"])
            completion(.failure(error))
        }
    }
}

// EXAMPLE 3: Different ways to handle Result
func demonstrateResultHandling() {
    print("3. DIFFERENT WAYS TO HANDLE RESULT:")
    
    fetchMoviesWithResult { result in
        // Method 1: Switch statement
        print("   Method 1 - Switch:")
        switch result {
        case .success(let movies):
            print("     Got \(movies.count) movies: \(movies)")
        case .failure(let error):
            print("     Error occurred: \(error.localizedDescription)")
        }
        
        // Method 2: Using .get() method (throws on failure)
        print("   Method 2 - Using .get():")
        do {
            let movies = try result.get()
            print("     Movies via .get(): \(movies)")
        } catch {
            print("     Error via .get(): \(error.localizedDescription)")
        }
        
        // Method 3: Using map and mapError
        print("   Method 3 - Using map:")
        let mappedResult = result
            .map { movies in "Found \(movies.count) movies" }
            .mapError { error in "Failed: \(error.localizedDescription)" }
        
        switch mappedResult {
        case .success(let message):
            print("     \(message)")
        case .failure(let message):
            print("     \(message)")
        }
    }
}

// EXAMPLE 4: Converting between Result and async/await
func fetchMoviesAsync() async throws -> [String] {
    return try await withCheckedThrowingContinuation { continuation in
        fetchMoviesWithResult { result in
            // Convert Result to async/await
            continuation.resume(with: result)
        }
    }
}

func demonstrateResultToAsync() async {
    print("4. CONVERTING RESULT TO ASYNC/AWAIT:")
    
    do {
        let movies = try await fetchMoviesAsync()
        print("   Async success: \(movies)")
    } catch {
        print("   Async error: \(error.localizedDescription)")
    }
    print()
}

// EXAMPLE 5: Creating your own Result-based functions
enum CustomError: Error, LocalizedError {
    case invalidInput
    case processingFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidInput:
            return "Invalid input provided"
        case .processingFailed:
            return "Processing failed"
        }
    }
}

func processNumber(_ number: Int, completion: @escaping (Result<String, CustomError>) -> Void) {
    print("5. CUSTOM RESULT-BASED FUNCTION:")
    
    DispatchQueue.global().asyncAfter(deadline: .now() + 0.5) {
        if number < 0 {
            completion(.failure(.invalidInput))
        } else if number > 100 {
            completion(.failure(.processingFailed))
        } else {
            completion(.success("Processed number: \(number * 2)"))
        }
    }
}

func demonstrateCustomResult() {
    processNumber(50) { result in
        switch result {
        case .success(let message):
            print("   \(message)")
        case .failure(let error):
            print("   Error: \(error.localizedDescription)")
        }
    }
}

// EXAMPLE 6: Result utility methods
func demonstrateResultMethods() {
    print("6. RESULT UTILITY METHODS:")
    
    let successResult: Result<Int, Error> = .success(42)
    let failureResult: Result<Int, Error> = .failure(CustomError.processingFailed)
    
    // Check if success/failure
    print("   Is success: \(successResult.isSuccess)")  // true
    print("   Is failure: \(failureResult.isFailure)")  // true
    
    // Get value or nil
    print("   Success value: \(successResult.successValue ?? -1)")  // 42
    print("   Failure value: \(failureResult.successValue ?? -1)")  // -1
    
    // Transform success values
    let doubled = successResult.map { $0 * 2 }
    print("   Doubled result: \(doubled)")  // .success(84)
    
    // Provide fallback values
    let valueOrDefault = failureResult.map { $0 * 2 } ?? .success(0)
    print("   With fallback: \(valueOrDefault)")  // .success(0)
    print()
}

// Extension to add convenience properties
extension Result {
    var isSuccess: Bool {
        switch self {
        case .success: return true
        case .failure: return false
        }
    }
    
    var isFailure: Bool {
        return !isSuccess
    }
    
    var successValue: Success? {
        switch self {
        case .success(let value): return value
        case .failure: return nil
        }
    }
    
    var failureValue: Failure? {
        switch self {
        case .success: return nil
        case .failure(let error): return error
        }
    }
}

// Run all demonstrations
func runAllDemonstrations() async {
    demonstrateBasicResult()
    demonstrateResultHandling()
    await demonstrateResultToAsync()
    demonstrateCustomResult()
    demonstrateResultMethods()
    
    print("=== KEY BENEFITS OF RESULT TYPE ===")
    print("• Explicit error handling - can't ignore errors")
    print("• Type-safe - compiler ensures you handle both cases")
    print("• Composable - can chain operations with map/flatMap")
    print("• Perfect for callback-based APIs")
    print("• Bridges well with async/await using continuations")
}

Task {
    await runAllDemonstrations()
    
    // Wait for async callbacks to complete
    try? await Task.sleep(nanoseconds: 2_000_000_000)
    exit(0)
}

RunLoop.main.run()
