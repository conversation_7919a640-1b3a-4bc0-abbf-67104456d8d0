import Foundation

print("=== DEMONSTRATING SYNC vs ASYNC CLOSURES ===\n")

// YOUR CODE: Synchronous closure
func fetchUserNameJay(_ completion: @escaping ([String]) -> Void) {
    print("  fetchUserNameJay: About to call completion")
    completion(["jay4", "jj"])  // Called immediately, synchronously
    print("  fetchUserNameJay: Completion called, about to return")
}

print("1. SYNCHRONOUS CLOSURE EXAMPLE:")
print("Before fetchUserNameJay")

fetchUserNameJay { s in
    print("Inside closure, got: \(s)")
    for i in s {
        print("  - \(i)")
    }
}

print("After fetchUserNameJay")
print("^ Notice: closure ran BEFORE this line!\n")

// CONTRAST: Asynchronous closure
func fetchUserNameAsync(_ completion: @escaping ([String]) -> Void) {
    print("  fetchUserNameAsync: Starting async work")
    
    // This makes it actually asynchronous
    DispatchQueue.global().async {
        print("  fetchUserNameAsync: Async work completed")
        completion(["async1", "async2"])
    }
    
    print("  fetchUserNameAsync: Returning immediately")
}

print("2. ASYNCHRONOUS CLOSURE EXAMPLE:")
print("Before fetchUserNameAsync")

fetchUserNameAsync { s in
    print("Inside async closure, got: \(s)")
    for i in s {
        print("  - \(i)")
    }
}

print("After fetchUserNameAsync")
print("^ Notice: this prints BEFORE the closure runs!")

// Wait to see the async closure execute
Thread.sleep(forTimeInterval: 1)
print()

// DEMONSTRATION: The @escaping keyword doesn't make it async
func synchronousButEscaping(_ completion: @escaping (String) -> Void) {
    print("  This function is still synchronous")
    completion("immediate result")
    print("  Even though the closure is @escaping")
}

print("3. @ESCAPING DOESN'T MEAN ASYNCHRONOUS:")
print("Before synchronousButEscaping")

synchronousButEscaping { result in
    print("Got result: \(result)")
}

print("After synchronousButEscaping")
print("^ The closure still ran synchronously!\n")

// EXPLANATION: What @escaping actually means
func storeClosureForLater(_ completion: @escaping (String) -> Void) {
    print("  Storing closure to call later...")
    
    // Store the closure to call it after the function returns
    DispatchQueue.global().asyncAfter(deadline: .now() + 1) {
        print("  Now calling the stored closure")
        completion("delayed result")
    }
    
    print("  Function returning (closure will be called later)")
}

print("4. WHAT @ESCAPING ACTUALLY MEANS:")
print("Before storeClosureForLater")

storeClosureForLater { result in
    print("Got delayed result: \(result)")
}

print("After storeClosureForLater")
print("^ Function returned, but closure hasn't run yet")

Thread.sleep(forTimeInterval: 2)
print()

print("=== KEY INSIGHTS ===")
print("• @escaping means the closure CAN outlive the function")
print("• It doesn't mean the closure WILL be called asynchronously")
print("• Your original code calls the closure immediately = synchronous")
print("• Async behavior requires explicit async mechanisms (DispatchQueue, etc.)")
