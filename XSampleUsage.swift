import Foundation

// Your function definition
func xsample(_ closure: () async throws -> Void) async throws {
    // no-op (but in real usage, this might do setup/teardown)
    try await closure()
}

// Example async functions to use in closures
func fetchData() async throws -> String {
    try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
    return "Fetched data"
}

func processData(_ data: String) async {
    await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
    print("Processed: \(data)")
}

// USAGE EXAMPLES

func demonstrateXSampleUsage() async throws {
    print("=== XSAMPLE USAGE EXAMPLES ===\n")
    
    // Example 1: Simple async work
    print("1. Simple async work:")
    try await xsample {
        try await Task.sleep(nanoseconds: 500_000_000)
        print("   Simple async work completed")
    }
    
    // Example 2: Multiple async operations
    print("2. Multiple async operations:")
    try await xsample {
        let data = try await fetchData()
        await processData(data)
        print("   Multiple operations completed")
    }
    
    // Example 3: Error handling in async closure
    print("3. Error handling:")
    do {
        try await xsample {
            // This might throw an error
            if Bool.random() {
                throw NSError(domain: "TestError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Random error occurred"])
            }
            try await Task.sleep(nanoseconds: 200_000_000)
            print("   No error occurred")
        }
    } catch {
        print("   Caught error: \(error.localizedDescription)")
    }
    
    // Example 4: Network request in closure
    print("4. Network request:")
    try await xsample {
        do {
            let url = URL(string: "https://httpbin.org/delay/1")!
            let (_, response) = try await URLSession.shared.data(from: url)
            if let httpResponse = response as? HTTPURLResponse {
                print("   Network request completed with status: \(httpResponse.statusCode)")
            }
        } catch {
            print("   Network request failed: \(error.localizedDescription)")
        }
    }
    
    // Example 5: Parallel async work
    print("5. Parallel async work:")
    try await xsample {
        async let task1 = fetchData()
        async let task2 = Task.sleep(nanoseconds: 800_000_000)
        
        let data = try await task1
        await task2
        print("   Parallel work completed with data: \(data)")
    }
}

// Different contexts where you can call xsample

// Context 1: From another async function
func callingFromAsyncFunction() async throws {
    print("Calling xsample from async function:")
    try await xsample {
        try await Task.sleep(nanoseconds: 300_000_000)
        print("   Called from async function")
    }
}

// Context 2: From a Task
func callingFromTask() {
    print("Calling xsample from Task:")
    Task {
        try await xsample {
            try await Task.sleep(nanoseconds: 300_000_000)
            print("   Called from Task")
        }
    }
}

// Context 3: From main async context
@main
struct XSampleDemo {
    static func main() async throws {
        try await demonstrateXSampleUsage()
        print()
        
        try await callingFromAsyncFunction()
        print()
        
        callingFromTask()
        
        // Wait a bit for the Task to complete
        try await Task.sleep(nanoseconds: 1_000_000_000)
        
        print("\n=== KEY POINTS ===")
        print("• Call xsample with 'try await'")
        print("• The closure automatically becomes async throws")
        print("• You can use await and try inside the closure")
        print("• Must be called from an async context")
    }
}
